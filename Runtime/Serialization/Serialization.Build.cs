// Copyright Epic Games, Inc. All Rights Reserved.

namespace UnrealBuildTool.Rules
{
	public class Serialization : ModuleRules
	{
		public Serialization(ReadOnlyTargetRules Target) : base(Target)
		{
			PublicDependencyModuleNames.AddRange(
				new string[] {
					"<PERSON>",
					"<PERSON><PERSON>",
					"<PERSON><PERSON>",
				});

			PrivateDependencyModuleNames.AddRange(
				new string[] {
					"CoreUObject",
				});
			UnsafeTypeCastWarningLevel = WarningLevel.Error;
		}
	}
}
