// Copyright Epic Games, Inc. All Rights Reserved.

#include "../Common.ush"
#include "../SceneData.ush"
#include "../WaveOpUtil.ush"
#include "../ComputeShaderUtils.ush"
#include "../VirtualShadowMaps/VirtualShadowMapPageOverlap.ush"
#include "../VirtualShadowMaps/VirtualShadowMapPageCacheCommon.ush"

#include "NaniteCullingCommon.ush"
#include "NaniteCulling.ush"
#include "NaniteDataDecode.ush"
#include "NaniteHZBCull.ush"
#include "NaniteWritePixel.ush"
#include "NaniteImposter.ush"

#include "../SceneCulling/SceneCulling.ush"

void DrawImposter( FNaniteView NaniteView, FInstanceSceneData InstanceData, uint InstanceId, float3 BoundsCenter, float3 BoundsExtent, uint ImposterIndex, FScreenRect Rect )
{
	// High bit flags it as an imposter
	uint ImposterPixelCode = (1u << 31) | (InstanceId << 8) | (1u << 7);

	bool bOrtho = NaniteView.ViewToClip[3][3] >= 1;

	for( int y = Rect.Pixels.y; y <= Rect.Pixels.w; y++ )
	{
		for( int x = Rect.Pixels.x; x <= Rect.Pixels.z; x++ )
		{
			uint2 PixelPos = uint2(x,y);

			// FIXME
			float2 Jitter = NaniteView.ViewToClip[2].xy * 0.5 * NaniteView.ViewSizeAndInvSize.xy;
			int FrameRand = (int)(Jitter.x * 417.0f);

			float2 Noise = Rand3DPCG16( int3( PixelPos, FrameRand ) ).xy / 65535.0;
			float2 PixelClip = ( ( PixelPos + 0.5 ) * NaniteView.ViewSizeAndInvSize.zw - 0.5 ) * float2(2, -2);

			FDFVector3 RayWorldOrigin;
			float3 RayWorldDirection;
			if( bOrtho )
			{
				float3 NearPoint = mul( float4( PixelPos + 0.5, 1, 1 ), NaniteView.SVPositionToTranslatedWorld ).xyz;
				float3 FarPoint  = mul( float4( PixelPos + 0.5, 0, 1 ), NaniteView.SVPositionToTranslatedWorld ).xyz;
				RayWorldOrigin = DFFastSubtract( NearPoint, NaniteView.PreViewTranslation );
				RayWorldDirection = FarPoint - NearPoint;
				Noise = 0.5;
			}
			else
			{
				RayWorldOrigin = NaniteView.WorldCameraOrigin;
				RayWorldDirection = mul( float4( PixelPos + 0.5, 0, 1 ), NaniteView.SVPositionToTranslatedWorld ).xyz;
			}
	
			FRay RayLocal;
			RayLocal.Origin		= DFMultiplyDemote( RayWorldOrigin, InstanceData.WorldToLocal );
			RayLocal.Direction	= DFMultiplyVector( RayWorldDirection, InstanceData.WorldToLocal );

			uint2 ImposterPixel = RayIntersectImposter( RayLocal, ImposterIndex, BoundsCenter, BoundsExtent, Noise );

			if( ImposterPixel.y != 0 )
			{
				float Depth = asfloat( ImposterPixel.y );
				//float4 HitClip = mul( float4( 0, 0, Depth, 1 ), NaniteView.ViewToClip );
				//float DeviceZ = HitClip.z / HitClip.w;
				float DeviceZ = NaniteView.ViewToClip[3][2] / Depth + NaniteView.ViewToClip[2][2];
				
				if( bOrtho )
					DeviceZ = 1 - Depth;

				uint TriIndex = ImposterPixel.x;
				uint PixelValue = ImposterPixelCode + TriIndex;
				WritePixel( OutVisBuffer64, PixelValue, NaniteView.ViewRect.xy + PixelPos, asuint( DeviceZ ) );

				#if 0//VISUALIZE
					WritePixel( OutDbgBuffer64, 0, PixelPos, asuint( DeviceZ ) );
					InterlockedAdd(OutDbgBuffer32[ PixelPos ], 1);
				#endif
			}
		}
	}
}

// Partially implements FPrimitiveSceneProxy::IsShown() - missing view filter lists and some misc. tests
bool IsPrimitiveShown(FPrimitiveSceneData PrimitiveData, uint RenderFlags, bool bIsShadowPass)
{
	const bool bCastShadow						= (PrimitiveData.Flags & PRIMITIVE_SCENE_DATA_FLAG_CAST_SHADOWS) != 0u;
	
	const bool bForceHidden						= (PrimitiveData.VisibilityFlags & PRIMITIVE_VISIBILITY_FLAG_FORCE_HIDDEN) != 0u;
	const bool bVisibleInGame					= (PrimitiveData.VisibilityFlags & PRIMITIVE_VISIBILITY_FLAG_VISIBLE_IN_GAME) != 0u;
	const bool bVisibleInEditor					= (PrimitiveData.VisibilityFlags & PRIMITIVE_VISIBILITY_FLAG_VISIBLE_IN_EDITOR) != 0u;
	const bool bVisibleInReflectionCaptures		= (PrimitiveData.VisibilityFlags & PRIMITIVE_VISIBILITY_FLAG_VISIBLE_IN_REFLECTION_CAPTURES) != 0u;
	//const bool bVisibleInRealTimeSkyCaptures	= (PrimitiveData.VisibilityFlags & PRIMITIVE_VISIBILITY_FLAG_VISIBLE_IN_REAL_TIME_SKY_CAPTURES) != 0u;
	//const bool bVisibleInRayTracing			= (PrimitiveData.VisibilityFlags & PRIMITIVE_VISIBILITY_FLAG_VISIBLE_IN_RAY_TRACING) != 0u;
	const bool bVisibleInSceneCaptureOnly		= (PrimitiveData.VisibilityFlags & PRIMITIVE_VISIBILITY_FLAG_VISIBLE_IN_SCENE_CAPTURE_ONLY) != 0u;
	const bool bHiddenInSceneCapture			= (PrimitiveData.VisibilityFlags & PRIMITIVE_VISIBILITY_FLAG_HIDDEN_IN_SCENE_CAPTURE) != 0u;
	const bool bVisibleInRayTracing				= (PrimitiveData.VisibilityFlags & PRIMITIVE_VISIBILITY_FLAG_VISIBLE_IN_RAY_TRACING) != 0u;
	const bool bCastHiddenShadow				= (PrimitiveData.VisibilityFlags & PRIMITIVE_VISIBILITY_FLAG_CAST_HIDDEN_SHADOW) != 0u;
	const bool bVisibleInLumenScene				= (PrimitiveData.VisibilityFlags & PRIMITIVE_VISIBILITY_FLAG_VISIBLE_IN_LUMEN_SCENE) != 0u;

	const bool bIsSceneCapture					= (RenderFlags & NANITE_RENDER_FLAG_IS_SCENE_CAPTURE) != 0u;
	const bool bIsReflectionCapture				= (RenderFlags & NANITE_RENDER_FLAG_IS_REFLECTION_CAPTURE) != 0u;
	const bool bIsGameView						= (RenderFlags & NANITE_RENDER_FLAG_IS_GAME_VIEW) != 0u;
	const bool bEditorShowFlag					= (RenderFlags & NANITE_RENDER_FLAG_EDITOR_SHOW_FLAG_ENABLED) != 0u;
	const bool bGameShowFlag					= (RenderFlags & NANITE_RENDER_FLAG_GAME_SHOW_FLAG_ENABLED) != 0u;
	const bool bIsLumenCapture					= (RenderFlags & NANITE_RENDER_FLAG_IS_LUMEN_CAPTURE) != 0u;

	if (bForceHidden)
	{
		// Primitive is forcibly hidden
		return false;
	}

	if (bIsShadowPass && !bCastShadow)
	{
		// Only shadow casting primitives are visible in a shadow pass
		return false;
	}

	if (bIsLumenCapture)
	{
		// No need to capture this primitive, as it isn't tracked by Lumen scene
		if (!bVisibleInLumenScene)
		{
			return false;
		}

		// Primitives may be visible only in ray tracing, but we still want to capture them into surface cache
		if (bVisibleInRayTracing)
		{
			return true;
		}
	}

	if (bIsSceneCapture)
	{
		if (bHiddenInSceneCapture)
		{
			return false;
		}
	}
	else if (bVisibleInSceneCaptureOnly)
	{
		return false;
	}

	if (bIsShadowPass && bCastHiddenShadow)
	{
		return true;
	}

	if (bEditorShowFlag)
	{
		if (!bVisibleInEditor)
		{
			return false;
		}
	}
	else
	{
		if (!bVisibleInGame)
		{
			return false;
		}

		// "G" mode in editor viewport
		if (!bIsGameView && bGameShowFlag && !bVisibleInEditor)
		{
			return false;
		}
	}

	return true;
}

//======================
// Instance culling
//
// Culls instances and outputs list of clusters to further cull.
//======================

uint NumInstances;
uint NumViews;
uint NumPrimaryViews;
int ImposterMaxPixels;

StructuredBuffer<FInstanceDraw>			InInstanceDraws;
Buffer<uint>							InOccludedInstancesArgs;

#if INSTANCE_CULL_USE_WORK_GROUP_BUFFER

Buffer<uint>								InInstanceWorkArgs;
StructuredBuffer<FInstanceCullingGroupWork> InInstanceWorkGroups;

// These paths are coupled for the time being
StructuredBuffer<FViewDrawGroup> InViewDrawRanges;

#endif 

#if PRIMITIVE_FILTER
StructuredBuffer<uint>					InPrimitiveFilterBuffer;
#endif

RWStructuredBuffer<FInstanceDraw>		OutOccludedInstances;
RWBuffer<uint>							OutOccludedInstancesArgs;
RWByteAddressBuffer						OutMainAndPostNodesAndClusterBatches;
RWStructuredBuffer<FQueueState>			OutQueueState;

#if DEBUG_FLAGS
RWStructuredBuffer<FNaniteStats>		OutStatsBuffer;
#endif


void WriteOccludedInstance(uint ViewId, uint InstanceId)
{
	uint OccludedInstanceOffset = 0;
	WaveInterlockedAddScalarInGroups(OutOccludedInstancesArgs[3], OutOccludedInstancesArgs[0], 64, 1, OccludedInstanceOffset);
	OutOccludedInstances[OccludedInstanceOffset].ViewId = ViewId;
	OutOccludedInstances[OccludedInstanceOffset].InstanceId = InstanceId;
}

[numthreads(64, 1, 1)]
void InstanceCull(
	uint3 GroupId : SV_GroupID,
	uint GroupThreadIndex : SV_GroupIndex)
{
	const uint DispatchIndex = GetUnWrappedDispatchThreadId(GroupId, GroupThreadIndex, 64);

	FViewDrawGroup ViewDrawGroup;
	ViewDrawGroup.FirstView = 0;
	ViewDrawGroup.NumViews = 1U;

	const bool bIsPostPass = (CULLING_PASS == CULLING_PASS_OCCLUSION_POST);

#if INSTANCE_CULL_USE_WORK_GROUP_BUFFER

	// First range of groups fetch data from the group work buffer (view-scalar -> refactor to drive by calling helper function? Only affects post-pass)
	uint NumInstanceWorkGroups = InInstanceWorkArgs[0];
	//uint NumInstanceWorkGroups = InNumInstanceWorkGroups[0];

#if DEBUG_FLAGS && CULLING_PASS == CULLING_PASS_OCCLUSION_POST
	if ((RenderFlags & NANITE_RENDER_FLAG_WRITE_STATS) != 0u && DispatchIndex == 0)
	{
		InterlockedAdd(OutStatsBuffer[0].NumPostInstancesPreCull, InInstanceWorkArgs[0]);
	}
#endif

	FInstanceCullingGroupWork GroupWorkItem = InInstanceWorkGroups[GroupId.x];

	bool bIsRLEPackedChunk = false;
	uint GroupNumInstances =  UnpackChunkInstanceCount(GroupWorkItem.PackedItemChunkDesc, bIsRLEPackedChunk);
	ViewDrawGroup = InViewDrawRanges[GroupWorkItem.ViewGroupId];
	uint ActiveViewMask = GroupWorkItem.ActiveViewMask;

#if DEBUG_FLAGS 
	if ((RenderFlags & NANITE_RENDER_FLAG_WRITE_STATS) != 0u && GroupThreadIndex == 0)
	{
#if CULLING_PASS == CULLING_PASS_OCCLUSION_POST
		// Add the occluded instances to the count
		InterlockedAdd(OutStatsBuffer[0].NumPostInstancesPreCull, GroupNumInstances);
#else // CULLING_PASS == CULLING_PASS_OCCLUSION_MAIN/NO_OCCLUSION
		InterlockedAdd(OutStatsBuffer[0].NumMainInstancesPreCull, GroupNumInstances * ViewDrawGroup.NumViews);
#endif
	}
#endif

	// Kill overflowing threads.
	if (GroupThreadIndex >= GroupNumInstances)
	{
		return;
	}


	// Unpack compressed ID OR load the instance ID from the buffer (this is the ID buffer prepped during hierarchy build).
	uint InstanceId = UnpackChunkInstanceId(bIsRLEPackedChunk, GroupWorkItem.PackedItemChunkDesc, GroupThreadIndex);
	{

#else // !INSTANCE_CULL_USE_WORK_GROUP_BUFFER
#if CULLING_PASS == CULLING_PASS_OCCLUSION_POST
	uint NumInstancesLocal = InOccludedInstancesArgs[3];

#if DEBUG_FLAGS
	if ((RenderFlags & NANITE_RENDER_FLAG_WRITE_STATS) != 0u && DispatchIndex == 0)
	{
		InterlockedAdd(OutStatsBuffer[0].NumPostInstancesPreCull, NumInstancesLocal);
	}
#endif

#else
	uint NumInstancesLocal = NumInstances;
#endif

	if (DispatchIndex < NumInstancesLocal)
	{
#if CULLING_PASS == CULLING_PASS_OCCLUSION_POST || CULLING_PASS == CULLING_PASS_EXPLICIT_LIST
		uint InstanceId = InInstanceDraws[DispatchIndex].InstanceId;
		ViewDrawGroup.FirstView = InInstanceDraws[DispatchIndex].ViewId;;
#else
		uint InstanceId = DispatchIndex;
#endif
#if NANITE_MULTI_VIEW
	#if CULLING_PASS == CULLING_PASS_NO_OCCLUSION || CULLING_PASS == CULLING_PASS_OCCLUSION_MAIN
		ViewDrawGroup.NumViews = NumViews;
	#endif
#endif

#endif // INSTANCE_CULL_USE_WORK_GROUP_BUFFER

		uint PrimitiveId;
		uint InstanceFlags;
		LoadInstancePrimitiveIdAndFlags(InstanceId, Scene.GPUScene.InstanceDataSOAStride, PrimitiveId, InstanceFlags);

		if (PrimitiveId == INVALID_PRIMITIVE_ID || (InstanceFlags & INSTANCE_SCENE_DATA_FLAG_HIDDEN) != 0u)
		{
			return;
		}

		FInstanceSceneData InstanceData = GetInstanceSceneData(InstanceId, false);

		BRANCH
		if (InstanceData.NaniteRuntimeResourceID == 0xFFFFFFFFu)
		{
			// Only process valid Nanite instances
			return;
		}

		FPrimitiveSceneData PrimitiveData = GetPrimitiveData(InstanceData.PrimitiveId);

	#if PRIMITIVE_FILTER
		BRANCH
		if ((InPrimitiveFilterBuffer[InstanceData.PrimitiveId >> 5u] & BitFieldMaskU32(1u, InstanceData.PrimitiveId & 31u)) != 0u)
		{
			// Primitive has been filtered out
			return;
		}
	#endif

		const bool bIsShadowPass = (DEPTH_ONLY != 0);
		
		BRANCH
		if (!IsPrimitiveShown(PrimitiveData, RenderFlags, bIsShadowPass))
		{
			// Primitive is not visible - cull it
			return;
		}

		const float3 LocalBoundsCenter = InstanceData.LocalBoundsCenter;
		const float3 LocalBoundsExtent = InstanceData.LocalBoundsExtent;

		const uint PrimLightingChannelMask = GetPrimitive_LightingChannelMask_FromFlags(PrimitiveData.Flags);

		for (uint ViewIndex = 0; ViewIndex < ViewDrawGroup.NumViews; ++ViewIndex)
		{
#if INSTANCE_CULL_USE_WORK_GROUP_BUFFER
			if ((ActiveViewMask & (1U << ViewIndex)) == 0U)
			{
				continue;
			}
#endif
			uint ViewId = ViewDrawGroup.FirstView + ViewIndex;

			FNaniteView NaniteView = GetNaniteView(ViewId);
			FInstanceDynamicData DynamicData = CalculateInstanceDynamicData(NaniteView, InstanceData);

			const bool bLightingChannelMismatch = NaniteView.bUseLightingChannelMask && ((NaniteView.LightingChannelMask & PrimLightingChannelMask) == 0u);

			BRANCH
			if (bLightingChannelMismatch)
			{
				continue;
			}

			FBoxCull Cull;
			Cull.Init( NaniteView, LocalBoundsCenter, LocalBoundsExtent, InstanceData.NonUniformScale, DynamicData.LocalToTranslatedWorld, DynamicData.PrevLocalToTranslatedWorld );
			if( CULLING_PASS == CULLING_PASS_OCCLUSION_POST )
			{
				Cull.bSkipCullFrustum = true;
				Cull.bSkipCullGlobalClipPlane = true;
			}

			Cull.Distance(PrimitiveData);
			bool bCacheAsStatic = false;

			Cull.GlobalClipPlane();

			BRANCH
			if( Cull.bIsVisible )
			{
#if VIRTUAL_TEXTURE_TARGET
				const bool bAllowWPO = VirtualShadowMapIsWPOAllowed(PrimitiveData, NaniteView.TargetLayerIndex);
				Cull.bEnableWPO = Cull.bEnableWPO && bAllowWPO;
				
				bCacheAsStatic = ShouldCacheInstanceAsStatic(InstanceId, (NaniteView.Flags & NANITE_VIEW_FLAG_UNCACHED), bAllowWPO);
				Cull.PageFlagMask = GetPageFlagMaskForRendering(bCacheAsStatic);
				Cull.bUseStaticOcclusion = bCacheAsStatic;

				float PixelEstRadius = CalcClipSpaceRadiusEstimate(Cull.bIsOrtho, InstanceData, DynamicData.LocalToTranslatedWorld, NaniteView.ViewToClip) * float(VSM_VIRTUAL_MAX_RESOLUTION_XY);
				Cull.bDetailGeometry = IsDetailGeometry(bCacheAsStatic, true, PixelEstRadius);
#endif
				Cull.FrustumHZB( false );
			}

		#if CULLING_PASS == CULLING_PASS_OCCLUSION_MAIN
			if( Cull.bWasOccluded )
			{
				WriteOccludedInstance(ViewId, InstanceId);
			}
		#endif

			// NOTE: Imposters not supported currently with virtual targets
#if CULLING_PASS != CULLING_PASS_EXPLICIT_LIST && !VIRTUAL_TEXTURE_TARGET && NANITE_IMPOSTERS_SUPPORTED
			// Draw imposters
			const bool bHasNaniteImposter = (PrimitiveData.Flags & PRIMITIVE_SCENE_DATA_FLAG_HAS_NANITE_IMPOSTER) != 0u;
			if (bHasNaniteImposter && Cull.bIsVisible && !Cull.bWasOccluded)
			{
				FFrustumCullData FrustumCull = BoxCullFrustum(LocalBoundsCenter, LocalBoundsExtent, DynamicData.LocalToTranslatedWorld, NaniteView.TranslatedWorldToClip, NaniteView.ViewToClip, Cull.bIsOrtho, Cull.bNearClip, true);
				FScreenRect Rect = GetScreenRect( NaniteView.ViewRect, FrustumCull, 4 );

				if( all( Rect.Pixels.zw - Rect.Pixels.xy <= ImposterMaxPixels ) )
				{
					uint ImposterIndex = PrimitiveData.PackedNaniteFlags & NANITE_IMPOSTER_INDEX_MASK;
					ImposterIndex = CondMask(ImposterIndex == NANITE_IMPOSTER_INDEX_MASK, INVALID_NANITE_IMPOSTER_INDEX, ImposterIndex);
					DrawImposter(NaniteView, InstanceData, InstanceId, LocalBoundsCenter, LocalBoundsExtent, ImposterIndex, Rect);
					Cull.bIsVisible = false;
				}
			}
#endif
		
			if( Cull.bIsVisible && !Cull.bWasOccluded )
			{
				uint NodeOffset = 0;
				uint QueueStateIndex = ( CULLING_PASS == CULLING_PASS_OCCLUSION_POST );
				WaveInterlockedAddScalar_( OutQueueState[ 0 ].PassState[ QueueStateIndex ].NodeWriteOffset, 1, NodeOffset );
				WaveInterlockedAddScalar(  OutQueueState[ 0 ].PassState[ QueueStateIndex ].NodeCount, 1 );

#if DEBUG_FLAGS
				if ((RenderFlags & NANITE_RENDER_FLAG_WRITE_STATS) != 0u)
				{
				#if CULLING_PASS == CULLING_PASS_OCCLUSION_POST
					WaveInterlockedAddScalar( OutStatsBuffer[0].NumPostInstancesPostCull, 1 );
				#else
					WaveInterlockedAddScalar( OutStatsBuffer[0].NumMainInstancesPostCull, 1 );
				#endif
				}
#endif

				if(NodeOffset < MaxNodes)
				{
					uint Flags = NANITE_CULLING_FLAG_TEST_LOD;
					if (Cull.bFallbackRaster)
					{
						Flags |= NANITE_CULLING_FLAG_FALLBACK_RASTER;
					}
					if (Cull.bEnableWPO)
					{
						Flags |= NANITE_CULLING_FLAG_ENABLE_WPO;
					}
					if (bCacheAsStatic)
					{
						Flags |= NANITE_CULLING_FLAG_CACHE_AS_STATIC;
					}

					FCandidateNode Node;
					Node.Flags = Flags;
					Node.ViewId = ViewId;
					Node.InstanceId = InstanceId;
					Node.NodeIndex = 0;
					Node.EnabledBitmask = NANITE_BVH_NODE_ENABLE_MASK;
					StoreCandidateNode( OutMainAndPostNodesAndClusterBatches, NodeOffset, Node, bIsPostPass );
				}
			}
		}
	}
}

struct FCompactedViewInfo
{
	uint StartOffset;
	uint NumValidViews;
};

RWStructuredBuffer< FPackedNaniteView > CompactedViewsOut;
RWStructuredBuffer< uint > CompactedViewsAllocationOut;

#if INPUT_VIEW_RANGES

RWStructuredBuffer< FViewDrawGroup > InOutViewDrawRanges;
uint NumViewRanges;

// Cube map (6 faces) with max mips is the current limiter
// In theory clipmaps could go to higher counts but they are practically limited to 32 currently
// due to the coarse page bitmask and a few other things
#define VSM_MAX_VIEWS_PER_GROUP (VSM_MAX_MIP_LEVELS*6)

groupshared uint OutputViewId[VSM_MAX_VIEWS_PER_GROUP];
groupshared uint OutputViewCount;
groupshared uint OutputViewRangeOffset;

[numthreads(VSM_MAX_VIEWS_PER_GROUP, 1, 1)]
void CompactViewsVSM_CS(
	uint GroupId : SV_GroupID,
	uint ThreadId : SV_GroupIndex)
{
	// Order-preserving compaction
	// Could be a wave ops, but portable wave size assumptions are inconvenient here
	// Fast enough to just do this scalar as long a we vectorize the actual copy below
	if (ThreadId == 0)
	{
		const uint ViewGroupIndex = GroupId.x;
		uint OutputViewOffset = 0U;

		FViewDrawGroup ViewDrawGroup = InOutViewDrawRanges[ViewGroupIndex];
		for (uint PrimaryViewId = ViewDrawGroup.FirstView; PrimaryViewId < ViewDrawGroup.FirstView + ViewDrawGroup.NumViews; ++PrimaryViewId)
		{
			FNaniteView PrimaryNaniteView = GetNaniteView(PrimaryViewId);

			for (uint TargetMipLevel = 0; TargetMipLevel < (uint)PrimaryNaniteView.TargetNumMipLevels; TargetMipLevel++)
			{
				uint MipViewId = TargetMipLevel * NumPrimaryViews + PrimaryViewId;
				FNaniteView MipView = GetNaniteView(MipViewId);

				// TODO: We shouldn't need to load the mip view for this data...
				// TargetLayerIndex should be the same as the primary, and miplevel should just be the analytic one
				uint4 RectPages = VirtualShadowMap.UncachedPageRectBounds[MipView.TargetLayerIndex * VSM_MAX_MIP_LEVELS + MipView.TargetMipLevel];

				if (all(RectPages.zw >= RectPages.xy))
				{
					OutputViewId[OutputViewOffset] = MipViewId;
					OutputViewOffset += 1U;
				}
			}
		}

#if DEBUG_FLAGS
		if ((RenderFlags & NANITE_RENDER_FLAG_WRITE_STATS) != 0u)
		{
			InterlockedAdd(OutStatsBuffer[0].NumPrimaryViews, ViewDrawGroup.NumViews);
			InterlockedAdd(OutStatsBuffer[0].NumTotalViews, OutputViewOffset);
		}
#endif

		OutputViewCount = OutputViewOffset;

		InterlockedAdd(CompactedViewsAllocationOut[1], OutputViewCount, OutputViewRangeOffset);
		{
			FViewDrawGroup ViewDrawGroupResult;
			ViewDrawGroupResult.FirstView = OutputViewRangeOffset;
			ViewDrawGroupResult.NumViews = OutputViewCount;
			InOutViewDrawRanges[ViewGroupIndex] = ViewDrawGroupResult;
		}
	}

	GroupMemoryBarrierWithGroupSync();

	// Copy any views that survived into their compacted locations
	if (ThreadId < OutputViewCount)
	{
		uint ViewId = OutputViewId[ThreadId];
		uint CompactedOutputOffset = OutputViewRangeOffset + ThreadId;
		CompactedViewsOut[CompactedOutputOffset] = InViews[ViewId];
	}
}

#else // INPUT_VIEW_RANGES

RWStructuredBuffer< FCompactedViewInfo > CompactedViewInfoOut;

// TODO: Make sure this always runs and prepend to the whole pipeline such that view groups are always a range
//       TBD how to manage that...
[numthreads(64, 1, 1)]
void CompactViewsVSM_CS(uint DispatchThreadId : SV_DispatchThreadID)
{
	if (DispatchThreadId >= NumPrimaryViews)
	{
		return;
	}
	FViewDrawGroup ViewDrawGroup;
	ViewDrawGroup.FirstView = DispatchThreadId;
	ViewDrawGroup.NumViews = 1U;

	uint NumValidViews = 0U;
	for (uint PrimaryViewId = ViewDrawGroup.FirstView; PrimaryViewId < ViewDrawGroup.FirstView + ViewDrawGroup.NumViews; ++PrimaryViewId)
	{
		FNaniteView PrimaryNaniteView = GetNaniteView(PrimaryViewId);

		for (uint TargetMipLevel = 0; TargetMipLevel < (uint)PrimaryNaniteView.TargetNumMipLevels; TargetMipLevel++)
		{
			uint MipViewId = TargetMipLevel * NumPrimaryViews + PrimaryViewId;
			FNaniteView MipView = GetNaniteView(MipViewId);

			uint4 RectPages = VirtualShadowMap.UncachedPageRectBounds[MipView.TargetLayerIndex * VSM_MAX_MIP_LEVELS + MipView.TargetMipLevel];

			if (all(RectPages.zw >= RectPages.xy))
			{
				NumValidViews += 1U;
			}
		}
	}
	// Neither primary nor mip views have valid pages
	if (NumValidViews == 0U)
	{
		return;
	}

	uint CompactedOutputOffset = 0;
	InterlockedAdd(CompactedViewsAllocationOut[1], NumValidViews, CompactedOutputOffset);
	{
		uint InfoOutputOffset = 0;
		InterlockedAdd(CompactedViewsAllocationOut[0], 1, InfoOutputOffset);

		FCompactedViewInfo CompactedViewInfo;
		CompactedViewInfo.StartOffset = CompactedOutputOffset;
		CompactedViewInfo.NumValidViews = NumValidViews;

		CompactedViewInfoOut[InfoOutputOffset] = CompactedViewInfo;
	}

	for (uint PrimaryViewId = ViewDrawGroup.FirstView; PrimaryViewId < ViewDrawGroup.FirstView + ViewDrawGroup.NumViews; ++PrimaryViewId)
	{
		FNaniteView PrimaryNaniteView = GetNaniteView(PrimaryViewId);

		for (uint TargetMipLevel = 0; TargetMipLevel < (uint)PrimaryNaniteView.TargetNumMipLevels; TargetMipLevel++)
		{
			uint MipViewId = TargetMipLevel * NumPrimaryViews + PrimaryViewId;
			FNaniteView MipView = GetNaniteView(MipViewId);

			uint4 RectPages = VirtualShadowMap.UncachedPageRectBounds[MipView.TargetLayerIndex * VSM_MAX_MIP_LEVELS + MipView.TargetMipLevel];

			if (all(RectPages.zw >= RectPages.xy))
			{
				CompactedViewsOut[CompactedOutputOffset] = InViews[MipViewId];
				CompactedOutputOffset += 1U;
			}
		}
	}
}

#endif // INPUT_VIEW_RANGES

void ProcessPrimaryView(const bool bIsOrtho, FPrimitiveSceneData PrimitiveData, const uint InstanceId, FInstanceSceneData InstanceData, FCompactedViewInfo ViewInfo, FNaniteView NaniteView, bool bHasMoved)
{
	const uint PrimLightingChannelMask = GetPrimitive_LightingChannelMask(InstanceData.PrimitiveId);
	const bool bLightingChannelMismatch = ((NaniteView.LightingChannelMask & PrimLightingChannelMask) == 0u);

	if (bLightingChannelMismatch)
	{
		return;
	}
	
	const float3 LocalBoundsCenter = InstanceData.LocalBoundsCenter;
	const float3 LocalBoundsExtent = InstanceData.LocalBoundsExtent;

	FInstanceDynamicData DynamicData = CalculateInstanceDynamicData(NaniteView, InstanceData);

	FBoxCull Cull;
	Cull.Init( NaniteView, LocalBoundsCenter, LocalBoundsExtent, InstanceData.NonUniformScale, DynamicData.LocalToTranslatedWorld, DynamicData.PrevLocalToTranslatedWorld );
	Cull.bIsOrtho = bIsOrtho;
	if( CULLING_PASS == CULLING_PASS_OCCLUSION_POST )
	{
		Cull.bSkipCullFrustum = true;
		Cull.bSkipCullGlobalClipPlane = true;
	}
	
	Cull.Distance( PrimitiveData );
	bool bCacheAsStatic = false;

	Cull.GlobalClipPlane();

	if( !Cull.bIsVisible )
		return;

	FFrustumCullData FrustumCull = Cull.Frustum();

	if( !Cull.bIsVisible )
		return;

	// This should be unnecessary since this shader is only ever compiled with this define, but some compilers get upset otherwise
#if VIRTUAL_TEXTURE_TARGET
	{
		const bool bAllowWPO = VirtualShadowMapIsWPOAllowed(PrimitiveData, NaniteView.TargetLayerIndex);
		Cull.bEnableWPO = Cull.bEnableWPO && bAllowWPO;

		bCacheAsStatic = ShouldCacheInstanceAsStatic(InstanceId, (NaniteView.Flags & NANITE_VIEW_FLAG_UNCACHED), bAllowWPO);
		Cull.PageFlagMask = GetPageFlagMaskForRendering(bCacheAsStatic);
		Cull.bUseStaticOcclusion = bCacheAsStatic;
		
		float PixelEstRadius = CalcClipSpaceRadiusEstimate(Cull.bIsOrtho, InstanceData, DynamicData.LocalToTranslatedWorld, NaniteView.ViewToClip) * float(VSM_VIRTUAL_MAX_RESOLUTION_XY);		
		Cull.bDetailGeometry = IsDetailGeometry(bCacheAsStatic, true, PixelEstRadius);
	}
#endif

	for (uint MipViewInfoIndex = 0; MipViewInfoIndex < ViewInfo.NumValidViews; ++MipViewInfoIndex)
	{
		uint MipViewId = ViewInfo.StartOffset + MipViewInfoIndex;
		FNaniteView MipView = GetNaniteView(MipViewId);
		float2 ViewSize = MipView.ViewSizeAndInvSize.xy;

		// TODO: minor optimization possible, but need to duplicate setup from CullRasterize for virtual targets
		//float2 ViewSize = float2( ( TargetViewSize + ( 1u << TargetMipLevel ) - 1u ) >> TargetMipLevel );
		
		Cull.NaniteView		= MipView;
		Cull.bIsVisible		= true;
		Cull.bWasOccluded	= false;
		Cull.HZB( FrustumCull, false );

	#if CULLING_PASS == CULLING_PASS_OCCLUSION_MAIN
		if (Cull.bWasOccluded)
		{
			WriteOccludedInstance(MipViewId, InstanceId);
		}
	#endif

		BRANCH
		if( Cull.bIsVisible && !Cull.bWasOccluded )
		{
			uint NodeOffset = 0;

			WaveInterlockedAdd_( OutQueueState[0].PassState[0].NodeWriteOffset, 1U, NodeOffset );
			WaveInterlockedAdd(  OutQueueState[0].PassState[0].NodeCount, 1U );
#if DEBUG_FLAGS
			if ((RenderFlags & NANITE_RENDER_FLAG_WRITE_STATS) != 0u)
			{
				WaveInterlockedAdd(OutStatsBuffer[0].NumMainInstancesPostCull, 1U);
			}
#endif

			// Output visible nodes. Get compaction for free by only looping over set bits in VisibleMipsMask.
			// Iteration count is equal to the maximum lane output count.
			if (NodeOffset < MaxNodes)
			{
				FCandidateNode Node;
				Node.Flags = NANITE_CULLING_FLAG_TEST_LOD;
				Node.ViewId = MipViewId;
				Node.InstanceId = InstanceId;
				Node.NodeIndex = 0;
				Node.EnabledBitmask = NANITE_BVH_NODE_ENABLE_MASK;

				if (Cull.bFallbackRaster)
				{
					Node.Flags |= NANITE_CULLING_FLAG_FALLBACK_RASTER;
				}
				if (Cull.bEnableWPO)
				{
					Node.Flags |= NANITE_CULLING_FLAG_ENABLE_WPO;
				}
				if (bCacheAsStatic)
				{
					Node.Flags |= NANITE_CULLING_FLAG_CACHE_AS_STATIC;
				}

				StoreCandidateNode( OutMainAndPostNodesAndClusterBatches, NodeOffset, Node, false );
			}
		}
	}
}


StructuredBuffer< FCompactedViewInfo > CompactedViewInfo;
StructuredBuffer< uint > CompactedViewsAllocation;

[numthreads(64, 1, 1)]
void InstanceCullVSM(uint3 GroupId : SV_GroupID, uint GroupIndex : SV_GroupIndex)
{
	const uint DispatchIndex = GetUnWrappedDispatchThreadId(GroupId, GroupIndex, 64);

	// View compaction stats
#if DEBUG_FLAGS
	if ((RenderFlags & NANITE_RENDER_FLAG_WRITE_STATS) != 0u && DispatchIndex == 0)
	{
		OutStatsBuffer[0].NumMainInstancesPreCull = NumInstances * CompactedViewsAllocation[1];
		OutStatsBuffer[0].NumPrimaryViews = CompactedViewsAllocation[0];
		OutStatsBuffer[0].NumTotalViews = CompactedViewsAllocation[1];
	}
#endif

	const uint InstanceId = DispatchIndex;

	BRANCH
	if (InstanceId >= NumInstances)
	{
		return;
	}

	uint PrimitiveId;
	uint InstanceFlags;
	LoadInstancePrimitiveIdAndFlags(InstanceId, Scene.GPUScene.InstanceDataSOAStride, PrimitiveId, InstanceFlags);

	if (PrimitiveId == INVALID_PRIMITIVE_ID || (InstanceFlags & INSTANCE_SCENE_DATA_FLAG_HIDDEN) != 0u)
	{
		return;
	}

	FInstanceSceneData InstanceData = GetInstanceSceneData(InstanceId, false);

	BRANCH
	if (InstanceData.NaniteRuntimeResourceID == 0xFFFFFFFFu)
	{
		// Only process valid Nanite instances
		return;
	}

#if PRIMITIVE_FILTER
	BRANCH
	if ((InPrimitiveFilterBuffer[InstanceData.PrimitiveId >> 5u] & BitFieldMaskU32(1u, InstanceData.PrimitiveId & 31u)) != 0u)
	{
		// Primitive has been filtered out
		return;
	}
#endif

	FPrimitiveSceneData PrimitiveData = GetPrimitiveData(PrimitiveId);

	BRANCH
	if (!IsPrimitiveShown(PrimitiveData, RenderFlags, /* bIsShadowPass = */ true))
	{
		// Primitive is not visible - cull it
		return;
	}

	const bool bHasMoved = GetGPUSceneFrameNumber() == InstanceData.LastUpdateSceneFrameNumber;

	// Loop over each of the views
	uint NumCompactedPrimaryViews = CompactedViewsAllocation[0];
	for (uint CompactedViewInfoIndex = 0; CompactedViewInfoIndex < NumCompactedPrimaryViews; ++CompactedViewInfoIndex)
	{
		FCompactedViewInfo ViewInfo = CompactedViewInfo[CompactedViewInfoIndex];
		uint PrimaryViewId = ViewInfo.StartOffset;

		FNaniteView NaniteView = GetNaniteView( PrimaryViewId );
	
		// Depth clipping should only be disabled with orthographic projections
		if (IsOrthoProjection(NaniteView.ViewToClip))
		{
			ProcessPrimaryView(true, PrimitiveData, InstanceId, InstanceData, ViewInfo, NaniteView, bHasMoved);
		}
		else
		{
			ProcessPrimaryView(false, PrimitiveData, InstanceId, InstanceData, ViewInfo, NaniteView, bHasMoved);
		}
	}
}
