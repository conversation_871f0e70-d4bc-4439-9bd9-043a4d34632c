// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/NS_LoadingScreen.h"
#include "Components/ProgressBar.h"
#include "UI/NS_UIManager.h"
#include "GameFlow/NS_GameInstance.h"

void UNS_LoadingScreen::NativeConstruct()
{
	Super::NativeConstruct();

}
void UNS_LoadingScreen::FakeUpdateProgress()
{
	FakeProgressMax = 3.0f; // 15초 동안 로딩이 진행된다고 가정
	TWeakObjectPtr<UNS_LoadingScreen> SafeThis = this;
	float ElapsedTime = 0.f;
	float PrevTime = GetWorld()->GetTimeSeconds();
	GetWorld()->GetTimerManager().SetTimer
	(
		LoadingTickHandle,
		FTimerDelegate::CreateLambda([SafeThis, ElapsedTime, PrevTime]() mutable
			{
				if (!SafeThis.IsValid())return;

				UWorld* World = GEngine->GetWorldFromContextObjectChecked(SafeThis.Get());

				if (!World)return;

				ElapsedTime += World->GetTimeSeconds() - PrevTime;
				float Alpha = FMath::Clamp(ElapsedTime / SafeThis->FakeProgressMax, 0.f, 1.f);
				float CurValue = FMath::Lerp(0.f, 1.f, Alpha);
				//UE_LOG(LogTemp, Warning, TEXT("ElapsedTime : %f / PrevTime :  %f / TimeSeconds :  %f "), FEmt.ElapsedTime, FEmt.PrevTime, GetWorld()->GetTimeSeconds());

				if (SafeThis->bIsLevelLoadComplete&& SafeThis->FakeProgressMax != 5.f)
				{
					SafeThis->FakeProgressMax = 5.f;
					ElapsedTime = 3.5f;
				}
				else
				{
					if (0.6f < CurValue)
						CurValue = 0.6f;
				}

				SafeThis->ProgressBar_Loading->SetPercent(CurValue);

				if (1.f <= CurValue)
				{
					SafeThis->ProgressBar_Loading->SetPercent(CurValue);
					if (UNS_GameInstance* GI = Cast<UNS_GameInstance>(World->GetGameInstance()))
					{
						if (GI->GetUIManager()->OnLoadingFinished.IsBound())
						{
							GI->GetUIManager()->OnLoadingFinished.Execute();
							GI->GetUIManager()->OnLoadingFinished.Unbind();
						}
					}

					World->GetTimerManager().ClearTimer(SafeThis->LoadingTickHandle);
				}
				else
					PrevTime = World->GetTimeSeconds();

			}), 0.05f, true
	);
}

void UNS_LoadingScreen::UpdateProgress()
{
	//ProgressBar_Loading->SetPercent(100.f);
	//if(1)return;

	TWeakObjectPtr<UNS_LoadingScreen> SafeThis = this;
	float ElapsedTime = 0.f;
	float PrevTime = GetWorld()->GetTimeSeconds();
	GetWorld()->GetTimerManager().SetTimer
	(
		LoadingTickHandle,
		FTimerDelegate::CreateLambda([SafeThis, ElapsedTime, PrevTime]() mutable
		{
			if (!SafeThis.IsValid())return;

			UWorld* World = SafeThis->GetWorld();

			if (!World)return;

			ElapsedTime += World->GetTimeSeconds() - PrevTime;
			float Alpha = FMath::Clamp(ElapsedTime / 3.f, 0.f, 1.f);
			float CurValue = FMath::Lerp(0.f, 1.f, Alpha);
			UE_LOG(LogTemp, Warning, TEXT("ElapsedTime : %f / PrevTime :  %f / CurValue :  %f "), ElapsedTime, PrevTime, CurValue);
			SafeThis->ProgressBar_Loading->SetPercent(CurValue);

			if (1.f <= CurValue)
			{
				SafeThis->ProgressBar_Loading->SetPercent(1.f);
				if (UNS_GameInstance* GI = Cast<UNS_GameInstance>(World->GetGameInstance()) )
				{
					if (GI->GetUIManager()->OnLoadingFinished.IsBound())
					{
						GI->GetUIManager()->OnLoadingFinished.Execute();
						GI->GetUIManager()->OnLoadingFinished.Unbind();
						UE_LOG(LogTemp, Warning, TEXT("OnLoadingFinished Execute ProgressBar_Loading 100 "));
					}
				}
			
				World->GetTimerManager().ClearTimer(SafeThis->LoadingTickHandle);
			}
			else
				PrevTime = World->GetTimeSeconds();

		}), 0.05f, true
	);
}

void UNS_LoadingScreen::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);

	//if (UNS_GameInstance* GI = Cast<UNS_GameInstance>(GetGameInstance()))
	//{
	//	//float Progress = GI->GetLoadingProgress();
	//	ProgressBar_Loading->SetPercent(InDeltaTime*0.01f);
	//}
}

bool UNS_LoadingScreen::CheckRenderingReadiness()
{
	UWorld* World = GetWorld();
	if (!World) return false;

	// 월드가 완전히 로드되었는지 확인
	if (World->AreActorsInitialized() && World->HasBegunPlay())
	{
		bIsRenderingReady = true;
		return true;
	}

	return false;
}

bool UNS_LoadingScreen::CheckFrameRateStability()
{
	if (!bIsRenderingReady) return false;

	// 현재 프레임률 계산
	float CurrentFrameRate = 1.0f / GetWorld()->GetDeltaSeconds();

	// 최근 프레임률 기록에 추가
	RecentFrameRates.Add(CurrentFrameRate);

	// 최대 60개의 프레임률 기록 유지 (약 3초간)
	if (RecentFrameRates.Num() > 60)
	{
		RecentFrameRates.RemoveAt(0);
	}

	// 충분한 데이터가 쌓였는지 확인
	if (RecentFrameRates.Num() >= 30) // 최소 1.5초간의 데이터
	{
		// 평균 프레임률 계산
		float AverageFrameRate = 0.0f;
		for (float FrameRate : RecentFrameRates)
		{
			AverageFrameRate += FrameRate;
		}
		AverageFrameRate /= RecentFrameRates.Num();

		// 프레임률이 안정적인지 확인
		if (AverageFrameRate >= MinRequiredFrameRate)
		{
			FrameRateCheckDuration += GetWorld()->GetDeltaSeconds();

			// 충분한 시간 동안 안정적이었는지 확인
			if (FrameRateCheckDuration >= StableFrameCheckTime)
			{
				bIsFrameRateStable = true;
				UE_LOG(LogTemp, Log, TEXT("프레임률 안정화 완료: 평균 %.1f FPS"), AverageFrameRate);
				return true;
			}
		}
		else
		{
			// 프레임률이 떨어지면 다시 측정 시작
			FrameRateCheckDuration = 0.0f;
			UE_LOG(LogTemp, Warning, TEXT("프레임률 불안정: 현재 %.1f FPS (요구: %.1f FPS)"), AverageFrameRate, MinRequiredFrameRate);
		}
	}

	return false;
}

void UNS_LoadingScreen::UpdateRealLoadingProgress()
{
	// 렌더링 준비 상태 체크
	if (!bIsRenderingReady)
	{
		CheckRenderingReadiness();
	}

	// 프레임률 안정성 체크
	if (bIsRenderingReady && !bIsFrameRateStable)
	{
		CheckFrameRateStability();
	}
}