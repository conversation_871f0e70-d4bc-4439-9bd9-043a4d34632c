// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/NS_LoadingScreen.h"
#include "Components/ProgressBar.h"
#include "UI/NS_UIManager.h"
#include "GameFlow/NS_GameInstance.h"

void UNS_LoadingScreen::NativeConstruct()
{
	Super::NativeConstruct();

}
void UNS_LoadingScreen::FakeUpdateProgress()
{
	// 가짜 로딩 진행 시간 설정 (3초 동안 로딩바가 채워짐)
	FakeProgressMax = 3.0f; 
	// 안전한 참조를 위해 현재 객체의 약한 포인터 생성
	TWeakObjectPtr<UNS_LoadingScreen> SafeThis = this;
	// 경과 시간 초기화
	float ElapsedTime = 0.f;
	// 이전 시간 기록 (델타 타임 계산용)
	float PrevTime = GetWorld()->GetTimeSeconds();
	// 타이머 설정 - 0.05초마다 반복 실행
	GetWorld()->GetTimerManager().SetTimer
	(
		LoadingTickHandle,
		// 람다 함수로 타이머 콜백 정의
		FTimerDelegate::CreateLambda([SafeThis, ElapsedTime, PrevTime]() mutable
			{
				// 객체가 유효한지 확인
				if (!SafeThis.IsValid())return;

				// 현재 월드 객체 가져오기
				UWorld* World = GEngine->GetWorldFromContextObjectChecked(SafeThis.Get());

				// 월드가 유효한지 확인
				if (!World)return;

				// 경과 시간 계산 (현재 시간 - 이전 시간)
				ElapsedTime += World->GetTimeSeconds() - PrevTime;
				// 진행률 계산 (0~1 사이 값으로 제한)
				float Alpha = FMath::Clamp(ElapsedTime / SafeThis->FakeProgressMax, 0.f, 1.f);
				// 선형 보간으로 부드러운 진행률 계산
				float CurValue = FMath::Lerp(0.f, 1.f, Alpha);
				//UE_LOG(LogTemp, Warning, TEXT("ElapsedTime : %f / PrevTime :  %f / TimeSeconds :  %f "), FEmt.ElapsedTime, FEmt.PrevTime, GetWorld()->GetTimeSeconds());

				if (SafeThis->bIsLevelLoadComplete&& SafeThis->FakeProgressMax != 5.f)
				{
					SafeThis->FakeProgressMax = 5.f;
					ElapsedTime = 3.5f;
				}
				else
				{
					if (0.6f < CurValue)
						CurValue = 0.6f;
				}

				SafeThis->ProgressBar_Loading->SetPercent(CurValue);

				if (1.f <= CurValue)
				{
					SafeThis->ProgressBar_Loading->SetPercent(CurValue);
					if (UNS_GameInstance* GI = Cast<UNS_GameInstance>(World->GetGameInstance()))
					{
						if (GI->GetUIManager()->OnLoadingFinished.IsBound())
						{
							GI->GetUIManager()->OnLoadingFinished.Execute();
							GI->GetUIManager()->OnLoadingFinished.Unbind();
						}
					}

					World->GetTimerManager().ClearTimer(SafeThis->LoadingTickHandle);
				}
				else
					PrevTime = World->GetTimeSeconds();

			}), 0.05f, true
	);
}

void UNS_LoadingScreen::UpdateProgress()
{
	//ProgressBar_Loading->SetPercent(100.f);
	//if(1)return;

	TWeakObjectPtr<UNS_LoadingScreen> SafeThis = this;
	float ElapsedTime = 0.f;
	float PrevTime = GetWorld()->GetTimeSeconds();
	GetWorld()->GetTimerManager().SetTimer
	(
		LoadingTickHandle,
		FTimerDelegate::CreateLambda([SafeThis, ElapsedTime, PrevTime]() mutable
		{
			if (!SafeThis.IsValid())return;

			UWorld* World = SafeThis->GetWorld();

			if (!World)return;

			ElapsedTime += World->GetTimeSeconds() - PrevTime;
			float Alpha = FMath::Clamp(ElapsedTime / 3.f, 0.f, 1.f);
			float CurValue = FMath::Lerp(0.f, 1.f, Alpha);
			UE_LOG(LogTemp, Warning, TEXT("ElapsedTime : %f / PrevTime :  %f / CurValue :  %f "), ElapsedTime, PrevTime, CurValue);
			SafeThis->ProgressBar_Loading->SetPercent(CurValue);

			if (1.f <= CurValue)
			{
				SafeThis->ProgressBar_Loading->SetPercent(1.f);
				if (UNS_GameInstance* GI = Cast<UNS_GameInstance>(World->GetGameInstance()) )
				{
					if (GI->GetUIManager()->OnLoadingFinished.IsBound())
					{
						GI->GetUIManager()->OnLoadingFinished.Execute();
						GI->GetUIManager()->OnLoadingFinished.Unbind();
						UE_LOG(LogTemp, Warning, TEXT("OnLoadingFinished Execute ProgressBar_Loading 100 "));
					}
				}
			
				World->GetTimerManager().ClearTimer(SafeThis->LoadingTickHandle);
			}
			else
				PrevTime = World->GetTimeSeconds();

		}), 0.05f, true
	);
}

void UNS_LoadingScreen::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);

	//if (UNS_GameInstance* GI = Cast<UNS_GameInstance>(GetGameInstance()))
	//{
	//	//float Progress = GI->GetLoadingProgress();
	//	ProgressBar_Loading->SetPercent(InDeltaTime*0.01f);
	//}
}