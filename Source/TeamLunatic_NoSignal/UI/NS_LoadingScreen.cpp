// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/NS_LoadingScreen.h"
#include "Components/ProgressBar.h"
#include "UI/NS_UIManager.h"
#include "GameFlow/NS_GameInstance.h"

// 위젯이 생성될 때 호출되는 초기화 함수
void UNS_LoadingScreen::NativeConstruct()
{
    // 부모 클래스의 NativeConstruct 호출
    Super::NativeConstruct();
}

// 가짜 로딩 진행 상태를 업데이트하는 함수 (실제 로딩과 무관하게 시각적 피드백 제공)
void UNS_LoadingScreen::FakeUpdateProgress()
{
    // 가짜 로딩 진행 시간 설정 (3초 동안 로딩바가 채워짐)
    FakeProgressMax = 3.0f; 
    // 안전한 참조를 위해 현재 객체의 약한 포인터 생성
    TWeakObjectPtr<UNS_LoadingScreen> SafeThis = this;
    // 경과 시간 초기화
    float ElapsedTime = 0.f;
    // 이전 시간 기록 (델타 타임 계산용)
    float PrevTime = GetWorld()->GetTimeSeconds();
    // 타이머 설정 - 0.05초마다 반복 실행
    GetWorld()->GetTimerManager().SetTimer
    (
        LoadingTickHandle,
        // 람다 함수로 타이머 콜백 정의
        FTimerDelegate::CreateLambda([SafeThis, ElapsedTime, PrevTime]() mutable
            {
                // 객체가 유효한지 확인
                if (!SafeThis.IsValid())return;

                // 현재 월드 객체 가져오기
                UWorld* World = GEngine->GetWorldFromContextObjectChecked(SafeThis.Get());

                // 월드가 유효한지 확인
                if (!World)return;

                // 경과 시간 계산 (현재 시간 - 이전 시간)
                ElapsedTime += World->GetTimeSeconds() - PrevTime;
                // 진행률 계산 (0~1 사이 값으로 제한)
                float Alpha = FMath::Clamp(ElapsedTime / SafeThis->FakeProgressMax, 0.f, 1.f);
                // 선형 보간으로 부드러운 진행률 계산
                float CurValue = FMath::Lerp(0.f, 1.f, Alpha);
                //UE_LOG(LogTemp, Warning, TEXT("ElapsedTime : %f / PrevTime :  %f / TimeSeconds :  %f "), FEmt.ElapsedTime, FEmt.PrevTime, GetWorld()->GetTimeSeconds());

                // 레벨 로딩이 완료되었고 FakeProgressMax가 5.0이 아닌 경우
                if (SafeThis->bIsLevelLoadComplete && SafeThis->FakeProgressMax != 5.f)
                {
                    // 로딩 시간을 5초로 연장하고 경과 시간을 3.5초로 설정 (빠르게 마무리하기 위함)
                    SafeThis->FakeProgressMax = 5.f;
                    ElapsedTime = 3.5f;
                }
                else
                {
                    // 레벨 로딩이 완료되지 않은 경우 진행률을 60%로 제한 (실제 로딩 완료 전까지 대기)
                    if (0.6f < CurValue)
                        CurValue = 0.6f;
                }

                // 로딩 진행 바 업데이트
                SafeThis->ProgressBar_Loading->SetPercent(CurValue);

                // 진행률이 100%에 도달한 경우
                if (1.f <= CurValue)
                {
                    // 로딩 진행 바를 최종 값으로 설정
                    SafeThis->ProgressBar_Loading->SetPercent(CurValue);
                    // 게임 인스턴스 가져오기
                    if (UNS_GameInstance* GI = Cast<UNS_GameInstance>(World->GetGameInstance()))
                    {
                        // 로딩 완료 이벤트가 바인딩되어 있는 경우 실행
                        if (GI->GetUIManager()->OnLoadingFinished.IsBound())
                        {
                            GI->GetUIManager()->OnLoadingFinished.Execute();
                            GI->GetUIManager()->OnLoadingFinished.Unbind();
                        }
                    }

                    // 타이머 중지
                    World->GetTimerManager().ClearTimer(SafeThis->LoadingTickHandle);
                }
                else
                    // 다음 업데이트를 위해 이전 시간 갱신
                    PrevTime = World->GetTimeSeconds();

            }), 0.05f, true // 0.05초마다 반복 실행, 루프 활성화
    );
}

// 실제 로딩 진행 상태를 업데이트하는 함수
void UNS_LoadingScreen::UpdateProgress()
{
    //ProgressBar_Loading->SetPercent(100.f);
    //if(1)return;

    // 안전한 참조를 위해 현재 객체의 약한 포인터 생성
    TWeakObjectPtr<UNS_LoadingScreen> SafeThis = this;
    // 경과 시간 초기화
    float ElapsedTime = 0.f;
    // 이전 시간 기록 (델타 타임 계산용)
    float PrevTime = GetWorld()->GetTimeSeconds();
    // 타이머 설정 - 0.05초마다 반복 실행
    GetWorld()->GetTimerManager().SetTimer
    (
        LoadingTickHandle,
        // 람다 함수로 타이머 콜백 정의
        FTimerDelegate::CreateLambda([SafeThis, ElapsedTime, PrevTime]() mutable
        {
            // 객체가 유효한지 확인
            if (!SafeThis.IsValid())return;

            // 현재 월드 객체 가져오기
            UWorld* World = SafeThis->GetWorld();

            // 월드가 유효한지 확인
            if (!World)return;

            // 경과 시간 계산 (현재 시간 - 이전 시간)
            ElapsedTime += World->GetTimeSeconds() - PrevTime;
            // 진행률 계산 (0~1 사이 값으로 제한, 3초 동안 진행)
            float Alpha = FMath::Clamp(ElapsedTime / 3.f, 0.f, 1.f);
            // 선형 보간으로 부드러운 진행률 계산
            float CurValue = FMath::Lerp(0.f, 1.f, Alpha);
            // 로그 출력 (디버깅용)
            UE_LOG(LogTemp, Warning, TEXT("ElapsedTime : %f / PrevTime :  %f / CurValue :  %f "), ElapsedTime, PrevTime, CurValue);
            // 로딩 진행 바 업데이트
            SafeThis->ProgressBar_Loading->SetPercent(CurValue);

            // 진행률이 100%에 도달한 경우
            if (1.f <= CurValue)
            {
                // 로딩 진행 바를 100%로 설정
                SafeThis->ProgressBar_Loading->SetPercent(1.f);
                // 게임 인스턴스 가져오기
                if (UNS_GameInstance* GI = Cast<UNS_GameInstance>(World->GetGameInstance()) )
                {
                    // 로딩 완료 이벤트가 바인딩되어 있는 경우 실행
                    if (GI->GetUIManager()->OnLoadingFinished.IsBound())
                    {
                        GI->GetUIManager()->OnLoadingFinished.Execute();
                        GI->GetUIManager()->OnLoadingFinished.Unbind();
                        // 로그 출력 (디버깅용)
                        UE_LOG(LogTemp, Warning, TEXT("OnLoadingFinished Execute ProgressBar_Loading 100 "));
                    }
                }
            
                // 타이머 중지
                World->GetTimerManager().ClearTimer(SafeThis->LoadingTickHandle);
            }
            else
                // 다음 업데이트를 위해 이전 시간 갱신
                PrevTime = World->GetTimeSeconds();

        }), 0.05f, true // 0.05초마다 반복 실행, 루프 활성화
    );
}

// 매 프레임마다 호출되는 틱 함수
void UNS_LoadingScreen::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
    // 부모 클래스의 NativeTick 호출
    Super::NativeTick(MyGeometry, InDeltaTime);

    // 주석 처리된 코드 - 게임 인스턴스에서 로딩 진행률을 가져와 업데이트하는 기능
    //if (UNS_GameInstance* GI = Cast<UNS_GameInstance>(GetGameInstance()))
    //{
    //	//float Progress = GI->GetLoadingProgress();
    //	ProgressBar_Loading->SetPercent(InDeltaTime*0.01f);
    //}
}